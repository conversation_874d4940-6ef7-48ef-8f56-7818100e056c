<template>
  <el-drawer :visible.sync="showDrawer" direction="rtl" size="50%" :with-header="false" :wrapper-closable="false">
    <div class="drawer_title">
      <div class="close_button" @click="showDrawer = false">
        <i class="el-icon-close"  />
      </div>
      <span>老用户转化规则新增/编辑</span>
    </div>
    <div class="drawer_package">
      <div class="addForm_package">
        <el-form ref="form" :model="formData" :rules="formRules" label-position="top" class="demo-ruleForm">
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line"></div>
              <span>基础信息</span>
            </div>
            <div>
              <el-form-item label="规则名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入规则名称"
                  maxlength="30"
                  show-word-limit
                  class="input-with-limit"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="规则备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入规则备注"
                  maxlength="100"
                  show-word-limit
                  class="input-with-limit"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </div>

          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line"></div>
              <span>老用户判定</span>
            </div>
            <div class="flex">
              <el-form-item label="判定标识" prop="judgeFiled" :rules="formRules.common">
                <el-select v-model="formData.judgeFiled" placeholder="请选择判定标识">
                  <el-option v-for="item in judgmentMark" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="老用户类型" prop="oldUserType" :rules="formRules.common">
                <el-select v-model="formData.oldUserType" placeholder="请选择老用户类型">
                  <el-option v-for="item in oldUserList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="判定条件" prop="judgeType" :rules="formRules.common">
                <el-select v-model="formData.judgeType" placeholder="请选择判定条件">
                  <el-option
                    v-for="item in judgmentCondition"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line"></div>
              <span>媒体设置</span>
            </div>
            <el-form-item label="是否默认规则" prop="isDefault" :rules="formRules.common">
              <el-radio-group v-model="formData.isDefault" @input="defaultRulesChange">
                <el-radio v-for="item in defaultRulesList" :key="item.value" :label="item.value">{{
                  item.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="formData.isDefault === 0" class="flex">
              <el-form-item label="撞库类型" prop="apiMatchType" :rules="formRules.common">
                <el-select v-model="formData.apiMatchType" placeholder="请选择撞库类型" @change="apiMatchTypeChange">
                  <el-option
                    v-for="item in clueMatchTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item v-if="showCascader && !isEmpty(formData.apiMatchType)" label="撞库媒体" prop="apiCodeList">
                <el-cascader
                  v-model="formData.apiCodeList"
                  :options="showMediaList"
                  :props="{multiple: true}"
                  clearable
                  filterable
                ></el-cascader>
              </el-form-item>
            </div>
          </div>
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line"></div>
              <span>采量规则</span>
            </div>
            <el-form-item label="订单判定" prop="orderJudge" :rules="formRules.common">
              <el-select
                v-model="formData.orderJudge"
                placeholder="请选择订单判定"
                :disabled="!isEmpty(formData.orderJudge)"
              >
                <el-option v-for="item in orderJudgeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <div class="flex">
              <el-form-item label="时间判定" prop="timeJudgeType">
                <el-select
                  clearable
                  v-model="formData.timeJudgeType"
                  placeholder="请选择时间判定"
                  @change="handleTimeJudgeTypeChange"
                >
                  <el-option v-for="item in timeJudgeTypes" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="!isEmpty(formData.timeJudgeType)" label="时间判定条件" prop="comparison">
                <el-select v-model="formData.comparison" style="display: inline-block; width: 100px">
                  <el-option
                    v-for="item in timeJudgeCondition"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  oninput="value=value.replace(/[^0-9]/g,'')"
                  @blur="formData.timeJudgeValue = $event.target.value"
                  style="display: inline-block; width: 120px;margin-right: 3px;"
                  v-model="formData.timeJudgeValue"
                ></el-input>
                <span>（天）</span>
              </el-form-item>
            </div>
            <div class="flex">
              <el-form-item
                label="是否参与撞库校验"
                prop="isCollisionCheck"
                :rules="formRules.common"
                style="min-width: 200px"
              >
                <el-radio-group v-model="formData.isCollisionCheck" @input="handleCollisionCheck">
                  <el-radio v-for="item in collisionCheck" :key="item.value" :label="item.value">{{
                    item.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="bg-container" v-if="formData.isCollisionCheck === 1">
              <el-form-item label="撞库判定" prop="matchType" :rules="formRules.common">
                <el-select v-model="formData.matchType" @change="handleMatchTypeChange">
                  <el-option
                    v-for="item in crackingTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item prop="clueLogic">
                <div class="flex gap-5px">
                  <el-radio-group v-model="formData.clueLogic" size="large" @input="handleClueLogicChange">
                    <el-radio-button v-for="item in clueDatabaseTypeList" :label="item.value">
                      {{ item.label }}
                      <el-tooltip :content="item.tips" placement="top">
                        <i class="el-icon-question" style="font-size: 14px" />
                      </el-tooltip>
                    </el-radio-button> </el-radio-group
                  ><span>（选中的线索库将参与撞库，并且将参与校验）</span>
                </div>
                <el-table
                  class="mt-10px"
                  ref="multipleTable"
                  :data="tableData"
                  :border="true"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column :selectable="tableSelectAble" type="selection" width="55"> </el-table-column>
                  <el-table-column label="线索库类型" prop="clueTypeName"> </el-table-column>
                  <el-table-column label="线索库" prop="clueName"> </el-table-column>
                  <el-table-column label="线索库是否参与校验" prop="isCheck">
                    <template slot-scope="scope">{{
                      formData.thirdMatchS.includes(scope.row.clueValue) ? '是' : '否'
                    }}</template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </div>
          <div class="form_view">
            <div class="form_view_title">
              <div class="title_line"></div>
              <span>状态</span>
            </div>
            <el-form-item label="状态" prop="status" :rules="formRules.common">
              <el-radio v-for="item in statusList" :key="item.value" v-model="formData.status" :label="item.value">{{
                item.label
              }}</el-radio>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <div class="mt-10px pr-10px" :style="{'text-align': 'right', width: '100%'}">
        <el-button @click="showDrawer = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">确认</el-button>
      </div>
  </el-drawer>
</template>
<script>
import {
  oldUserList,
  judgmentMark,
  judgmentCondition,
  orderJudgeList,
  timeJudgeCondition,
  crackingTypeList,
  defaultRulesList,
  clueDataList,
  clueDatabaseTypeList,
  timeJudgeTypes,
  statusList,
  collisionCheck,
  INIT_FORM_DATA,
  clueMatchTypeList,
  handleLeafData,
  CLUEDATA_TYPE,
  CRACKING_TYPE,
  DEFAULT_RULES_MAP
} from '@/views/operate/module/oldUserConversionManage/baseData'
import {cloneDeep} from 'lodash'
import utils from '@/libs/utils'
// import {mediaApiMatchType} from '@/api/operate'
const {isEmpty} = utils
export default {
  name: 'editForm',
  props: {
    modelForm: {
      type: Object,
      default: () => ({})
    },
    mediaList: {
      type: Array,
      default: () => []
    },
    show: {
      type: Boolean,
      default: false
    },
    mediaFromClue: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showDrawer: true,
      formData: cloneDeep(INIT_FORM_DATA),
      tableData: [],
      judgmentMark,
      judgmentCondition,
      orderJudgeList,
      timeJudgeTypes,
      timeJudgeCondition,
      crackingTypeList,
      clueDatabaseTypeList,
      defaultRulesList,
      oldUserList,
      statusList,
      collisionCheck,
      // mediaFromClue: [],
      clueMatchTypeList,
      showMediaList: [],
      showCascader: true, //showMediaList异步处理值的时候el-Cascader内部会报错
      formRules: {
        common: [{required: true, message: '请选择', trigger: 'change'}],
        name: [{required: true, message: '请输入规则名称', trigger: 'blur'}],
        apiCodeList: [{required: true, message: '请选择撞库媒体', trigger: 'change'}],
        comparison: [
          {required: true, message: '请选择时间判定类型', trigger: 'change'},
          {
            validator: (rule, value, callback) => {
              if (!isEmpty(value) && isEmpty(this.formData.timeJudgeValue)) {
                return callback(new Error('请输入时间判定条件天数'))
              } else if (isEmpty(value)) {
                return callback(new Error('请选择时间判定条件'))
              }
              callback()
            },
            trigger: 'change'
          }
        ],
        clueLogic: [
          {
            required: true,
            message: '请选择线索库类型',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              if (!isEmpty(value) && (!this.formData.thirdMatchS || this.formData.thirdMatchS.length === 0)) {
                return callback(new Error('请选择线索库'))
              }
              callback()
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  watch: {
    showDrawer(val) {
      this.$emit('update:show', val)
    },
    'formData.apiMatchType': {
      handler() {
        this.getCurApiCodeList()
      },
      immediate: true
    }
  },
  mounted() {
    // let {data} = await mediaApiMatchType()
    // this.mediaFromClue = data || []
    this.formData = {...this.formData, ...cloneDeep(this.modelForm)}
    if (!isEmpty(this.formData.id)) {
      this.handleClueTableData()
      this.handleTableCheck()
    }
  },
  methods: {
    getCurApiCodeList() {
      this.showCascader = false
      let clueApiCode =
        this.mediaFromClue?.filter(item => item.apiMatchType === this.formData.apiMatchType)?.map(el => el.apiCode) ??
        []
      let mediaList = this.mediaList.filter(item => clueApiCode.includes(item.platformCode))
      this.showMediaList = handleLeafData(mediaList) || []
      this.$nextTick(() => {
        this.showCascader = true
      })
    },
    apiMatchTypeChange() {
      this.formData.apiCodeList = []
    },
    handleCollisionCheck() {
      let {matchType, clueLogic, thirdMatchS} = INIT_FORM_DATA
      this.formData.matchType = matchType
      this.formData.clueLogic = clueLogic
      this.formData.thirdMatchS = thirdMatchS
    },
    handleTimeJudgeTypeChange() {
      let {timeJudgeValue, comparison} = INIT_FORM_DATA
      this.formData.timeJudgeValue = timeJudgeValue
      this.formData.comparison = comparison
    },
    isEmpty,
    handleSelectionChange(val) {
      this.formData.thirdMatchS = val?.map(item => item.clueValue) ?? []
    },
    handleMatchTypeChange() {
      let {clueLogic} = this.formData
      this.handleClueTableData()
      if (!isEmpty(clueLogic)) {
        setTimeout(() => {
          this.handleClueLogicChange()
        }, 10)
      }
    },
    handleClueTableData() {
      let {matchType} = this.formData
      let clueTypeName = this.crackingTypeList.find(i => String(i.value) === String(matchType))?.label
      this.tableData = clueDataList.reduce((pre, cur) => {
        if (cur.clueType === matchType || matchType == CRACKING_TYPE.ALL) {
          pre.push({...cur, clueTypeName})
        }
        return pre
      }, [])
    },
    handleClueLogicChange() {
      let {clueLogic} = this.formData
      if (this.tableData?.length) {
        if (clueLogic == CLUEDATA_TYPE.ANY_ONE || clueLogic == CLUEDATA_TYPE.ALL) {
          this.tableData.forEach(row => {
            this.$refs?.multipleTable?.toggleRowSelection(row, true)
          })
        }
      }
    },
    handleTableCheck() {
      let {thirdMatchS} = this.formData
      if (this.tableData?.length) {
        this.$nextTick(() => {
          this.tableData.forEach(row => {
            if (thirdMatchS?.includes(row.clueValue) || thirdMatchS?.includes(String(row.clueValue))) {
              this.$refs?.multipleTable?.toggleRowSelection(row, true)
            }
          })
        })
      }
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = cloneDeep(this.formData)
          this.$emit('submit', formData)
        }
      })
    },
    tableSelectAble(row, index) {
      let {clueLogic} = this.formData
      return !(clueLogic == CLUEDATA_TYPE.ANY_ONE || clueLogic == CLUEDATA_TYPE.ALL)
    },
    defaultRulesChange() {
      if (this.formData.isDefault === DEFAULT_RULES_MAP.YES) {
        let {apiMatchType, apiCodeList} = INIT_FORM_DATA
        this.formData.apiMatchType = apiMatchType
        this.formData.apiCodeList = apiCodeList
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.close_button {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;
  i {
    color: white;
    line-height: 40px;
  }
}
.drawer_title {
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 9999;
  background-color: #fff;
  span {
    margin-left: 10px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }
}
.drawer_package {
  padding: 0 20px;
  background-color: #fff;
  position: relative;
  height: calc(100vh - 100px);
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  background-color: rgb(189, 184, 184, 0.1);
}

.addForm_package {
  padding: 15px 15px;
  // height: 100%;
  height: auto;

  .demo-ruleForm {
    width: 100%;
  background-color: #ffffff;

    // height: 100%;
    position: relative;
  }
}

.form_view {
  background-color: #ffffff;
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}
/* 解决 添加 filterable placeholder 重影 */
::v-deep {
  .el-cascader__tags input::-webkit-input-placeholder {
    color: white;
    opacity: 0;
  }
}
.input-with-limit {
  ::v-deep .el-input__inner {
    padding-right: 50px;
  }
}
.flex {
  display: flex;
  flex-wrap: wrap;
  column-gap: 50px;
  &.gap-5px {
    gap: 5px;
  }
}
.bg-container {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 10px 10px;
}
.mt-10px {
  margin-top: 10px;
}
.pr-10px{
  padding-right: 10px;
}
</style>
