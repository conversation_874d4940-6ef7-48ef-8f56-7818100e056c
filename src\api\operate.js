import CONSTANT from '@/config/constant.conf'
import {del, get, post, put, deleteWithBody} from '@/libs/axios.package'
import qs from 'qs'

/**
 * 贷款产品列表
 */
export const GET_LOAN_PRODUCT_LIST = obj => {
  return get('/loan/product/list', obj)
}

/*
 * 添加产品
 */
export const ADD_LOAN_PRODUCT = obj => {
  return post(`/loan/product`, obj)
}

/*
 * 修改产品
 */
export const PUT_LOAN_PRODUCT = obj => {
  return put(`/loan/product`, obj)
}

/*
 * 产品详情
 */
export const GET_LOAN_PRODUCT_EDIT = obj => {
  return get(`/loan/product/${obj.id}`)
}

/*
 * 删除产品
 */
export const DEL_LOAN_PRODUCT = obj => {
  return del(`/loan/product/${obj.id}`)
}

/**
 * 渠道列表
 */
export const GET_CHANNEL_LIST = obj => {
  return get('/channel/channelList', obj)
}

/*
 * 新增渠道
 */
export const ADD_CHANNEL = obj => {
  return post('/channel/addChannel', obj)
}

/*
 * NEW 新增渠道 - 1.4.2
 */
export const ADD_CHANNEL_NEW = obj => {
  return post('/v1/channel/add', obj)
}

/*
 * 更新渠道
 */
export const PUT_CHANNEL = obj => {
  return post('/channel/updateChannel', obj)
}
/*
 * NEW-更新渠道-1.4.2
 */
export const PUT_CHANNEL_NEW = obj => {
  return post(`/v1/channel/update/${obj.id}`, obj)
}
/*
 * 渠道详情
 */
export const GET_CHANNEL_EDIT = obj => {
  return post('/channel/getDetailById', obj)
}

// 渠道详情
export const GET_CHANNEL_EDIT_NEW = obj => {
  return get(`/v1/channel/get/${obj.id}`, obj)
}

/*
 * 删除渠道
 */
export const DEL_CHANNEL_EDIT = obj => {
  return post('/channel/deleteChannel', obj)
}

/**
 * 新增渠道类型
 */
export const add_channelType = obj => {
  return post('/channelType/saveChannelType', obj)
}

/**
 * 编辑渠道类型
 */
export const put_channelType = obj => {
  return post('/channelType/updateChannelType', obj)
}

/**
 * 删除渠道类型
 */
export const del_channelType = obj => {
  return post('channelType/deleteChannelType', obj)
}

/**
 * 渠道类型详情
 */
export const get_edit_channelType = obj => {
  return post('/channelType/selectChannelTypeById', obj)
}

/**
 * 渠道类型列表
 */
export const get_channelType_list = obj => {
  return post('/channelType/selectChannelTypeList', obj)
}

/**
 * 新-渠道列表 1.4.2
 */
export const GET_CHANNEL_LIST_NEW = obj => {
  return get('/channel/list', obj)
}

/**
 * 新-渠道列表 1.4.2 -删除
 */
export const DELETE_CHANNEL = obj => {
  return get(`/v1/channel/delete/${obj.id}`, obj)
}

/**
 * 渠道落地页列表
 */
export const get_channelStyles_list = obj => {
  return get('/channel/channelStyleList', obj)
}

/*
 * 获取活动商品列表
 */
export const getActiveCommoditiesApi = obj => {
  return get('/goods/extension/activity/list', obj)
}
/*
 * 获取活动商品详情
 */
export const getActiveCommoditiesDetailApi = id => {
  return get('/goodsActivity/' + id, null)
}
/*
 * 新增or编辑零元购商品
 */
export const setZeroPurchaseApi = obj => {
  return post('/goodsActivity/save', obj, null)
}
/*
 * 删除0元购活动商品
 */
export const deletActiveCommoditiesApi = id => {
  return del('/goodsActivity/' + id, null, null)
}

/** *********  新增橱窗  ***********/
export const addNavigate = obj => {
  return post('/navigate', obj, null)
}

/** *********  新增橱窗  ***********/
export const delNavigate = id => {
  return del(`/navigate/${id}`, null, null)
}

/** *********  新增橱窗  ***********/
export const editNavigate = obj => {
  return put(`/navigate`, obj)
}

/** *********  新增橱窗  ***********/
export const getNavigateById = id => {
  return get(`/navigate/${id}`, null)
}

/** *********  新增橱窗  ***********/
export const getNavigateList = obj => {
  return get('/navigate/list', obj)
}

/** *********  获取内链选项  ***********/
export const getLinkOptions = obj => {
  return get('navigate/linkoptions', obj)
}

/** *********  常见问题列表  ***********/
export const getCommonProblemList = obj => {
  return get('/common_problem/get_page_list', obj)
}

/** *********  常见问题列表删除  ***********/
export const delCommonProblem = obj => {
  return get('/common_problem/delete', obj)
}

/** *********  常见问题修改  ***********/
export const editCommonProblem = obj => {
  return post('/common_problem/update', obj, null)
}

/** *********  常见问题新增  ***********/
export const addCommonProblem = obj => {
  return post('/common_problem/save', obj, null)
}

/*
 * banner位置 1.4.1
 */
export const GET_LOCATIONLIST = obj => {
  return get('/banner/locationlist', obj)
}

/*
 * banner列表 1.4.1
 */
export const GET_BANNER_LIST = obj => {
  return get('/banner/list', obj)
}

/*
 * 编辑banner 1.4.1
 */
export const GET_BANNER_DETAIL = id => {
  return get(`/banner/${id}`)
}

/*
 * 新增banner 1.4.1
 */
export const ADD_BANNER = obj => {
  return post(`/banner`, obj)
}

/*
 * 编辑banner 1.4.1
 */
export const PUT_BANNER = obj => {
  return put(`/banner`, obj)
}

/*
 * 删除banner 1.4.1
 */
export const DELETE_BANNER = id => {
  return del(`/banner/${id}`)
}

/** *********  专题页列表  ***********/
export const GET_Special_Topic_LIST = obj => {
  return get('/feature/pages', obj)
}

/** *********  专题页新增  ***********/
export const ADD_Special_Topic = obj => {
  return post('/feature/pages', obj)
}

/** *********  专题页修改  ***********/
export const EDIT_Special_Topic = obj => {
  return put(`/feature/pages/${obj.id}`, obj)
}

/** *********  专题页删除  ***********/
export const DEL_Special_Topic = id => {
  return del(`/feature/pages/${id}`, null)
}

/** *********  专题页通过Id获取详情  ***********/
export const GET_Special_Topic_By_Id = id => {
  return get(`/feature/pages/${id}`, null)
}

/** *********  专题页模块列表  ***********/
export const GET_Models_Topic_LIST = params => {
  return get(`/feature/models/pages/${params.id}`, params)
}

/** *********  专题页模块新增  ***********/
export const ADD_Models_Topic = obj => {
  return post('/feature/models', obj)
}

/** *********  专题页模块修改  ***********/
export const EDIT_Models_Topic = obj => {
  return put(`/feature/models/${obj.id}`, obj)
}

/** *********  专题页模块通过Id获取详情  ***********/
export const GET_Models_Topic_By_Id = id => {
  return get(`/feature/models/${id}`, null)
}

/** *********  专题页组件列表  ***********/
export const GET_Component_Topic_LIST = obj => {
  return get('/feature/models', obj)
}

/** *********  专题页商品列表  ***********/
export const GET_Goods_Topic_LIST = obj => {
  return get('/feature/goods', obj)
}

/** *********  专题页统计  ***********/
export const GET_SPECIAL_TOPIC_STATISTICS = obj => {
  return get('/feature/statistic', obj)
}

/** *********  专题页统计  ***********/
export const EXPORT_SPECIAL_TOPIC_STATISTICS = data =>
  CONSTANT.publicPath + '/feature/statistic/export?' + qs.stringify(data)

/** *********  专题页商品新增  ***********/
export const ADD_Goods_Topic = obj => {
  return post('/feature/goods', obj)
}

/** *********  专题页商品修改  ***********/
export const EDIT_Goods_Topic = obj => {
  return put(`/feature/goods/${obj.id}`, obj)
}

/** *********  专题页商品删除  ***********/
export const DEL_Goods_Topic = id => {
  return del(`/feature/goods/${id}`, null)
}

/** *********  专题页商品通过Id获取详情  ***********/
export const GET_Goods_Topic_By_Id = id => {
  return get(`/feature/goods/${id}`, null)
}

/** 弹框 -- 列表 */
export const GET_Popup_Location = obj => {
  return get(`/popups/location`, obj)
}

export const GET_Popup_List = obj => {
  return get(`/popups`, obj)
}

export const Add_Popup = obj => {
  return post(`/popups`, obj)
}

export const EDIT_Popup = obj => {
  return put(`/popups/${obj.id}`, obj)
}

export const GET_POPUP_DETAIL_BY_ID = id => {
  return get(`/popups/${id}`, null)
}

/** 弹框 -- 规则 */
export const GET_Popup_Rules_All = obj => {
  return get(`/popup/rules/all`, null)
}

export const GET_Popup_Rules = obj => {
  return get(`/popup/rules/page`, obj)
}

export const EDIT_Popup_Rules = obj => {
  return put(`/popup/rules/${obj.id}`, obj.data)
}

/** 弹框 -- 统计 */
export const GET_Popup_Counts = obj => {
  return get(`/popup/counts`, obj)
}

export const GET_Popup_Counts_By_Id = obj => {
  return get(`/popup/counts/${obj.id}`, obj)
}

export const GET_SPLASH_SCREEN_LIST = obj => {
  return get(`/base/screen/page`, obj)
}

export const GET_SPLASH_SCREEN_BY_ID = id => {
  return get(`/base/screen/${id}`, null)
}

export const ADD_SPLASH_SCREEN = obj => {
  return post(`/base/screen`, obj)
}

export const EDIT_SPLASH_SCREEN = obj => {
  return put(`/base/screen`, obj)
}

export const GET_SELECT_STATIS_BY_PAGE_ID = id => {
  return get(`/bury/open/page/selectStatisByPageId/${id}`, null)
}

// 第三方专题
export const get_Special_Channel_FindList = () => {
  return get(`/v1/special/channel/findList`)
}

export const get_Special_Assembly_Save = obj => {
  return post(`/v1/special/assembly/save`, obj)
}

export const get_Special_Assembly_Details = obj => {
  return get(`/v1/special/assembly/details/${obj.id}`)
}

export const get_Special_Assembly_QueryList = obj => {
  return get(`/v1/special/assembly/queryList`, obj)
}

export const del_Special_Assembly_ById = id => {
  return del(`/v1/special/assembly/delete/${id}`)
}

export const GET_PORTAL_LINK_SOURCE = obj => {
  return get(`/banner/portalLinkSource`, obj)
}

export const GET_Special_Details_LinkType = obj => {
  return get(`/v1/special/details/linkType/${obj.type}`)
}

export const GET_SEARCH_HOT_WORD = obj => {
  return get(`/search/hotWord`, obj)
}

export const GET_SEARCH_HOT_WORD_BY_ID = id => {
  return get(`/search/hotWord/${id}`, null)
}

export const ADD_SEARCH_HOT_WORD = obj => {
  return post(`/search/hotWord`, obj)
}

export const EDIT_SEARCH_HOT_WORD = obj => {
  return put(`/search/hotWord`, obj)
}

export const GET_POPUP_RULE_CHANNEL_LIST = obj => {
  return get(`/popuprulechannel/list`, obj)
}

export const GET_POPUP_LIST_CHANNEL = obj => {
  return get(`/popuprulechannel/listChannel`, obj)
}

export const ADD_POPUP_RULE_CHANNEL = obj => {
  return post(`/popuprulechannel/add`, obj)
}

export const EDIT_POPUP_RULE_CHANNEL = obj => {
  return post(`/popuprulechannel/update`, obj)
}

export const GET_LIST_ALL_POPUP_CHANNEL_RULE = obj => {
  return get(`/popuprulechannel/listallpopupchannelrule`, obj)
}

export const GET_NEW_USER_GUIDANCE_ITEM = obj => {
  return get(`/count/newuser/guidance/item/page`, obj)
}

export const EXPORT_NEW_USER_GUIDANCE_ITEM = data =>
  CONSTANT.publicPath + '/count/newuser/guidance/item/export?' + qs.stringify(data)

export const countHfivePage = obj => {
  return get(`/count/hfive/page`, obj)
}

export const EXPORT_Page_Graborderlist = data => CONSTANT.publicPath + '/count/hfive/page/export?' + qs.stringify(data)

export const countHfivePagev2 = obj => {
  return get(`/count/hfive/page/v2`, obj)
}

export const EXPORT_Page_Graborderlistv2 = data =>
  CONSTANT.publicPath + '/count/hfive/page/export/v2?' + qs.stringify(data)

export const countHfivePagev2Detail = obj => {
  return get(`/count/hfive/page/v2/detail`, obj)
}

export const EXPORT_Page_Graborderlistv2Detail = data =>
  CONSTANT.publicPath + '/count/hfive/page/export/v2/detail?' + qs.stringify(data)
// 站内投诉数据统计
export const countAppFeedbackPage = obj => {
  return get(`/count/app/feedback/page`, obj)
}

export const exportCountAppFeedbackPage = data =>
  CONSTANT.publicPath + '/count/app/feedback/page/export?' + qs.stringify(data)

export const trendSearchNewConfigGet = obj => {
  return get(`/1688/config/get`, obj)
}

export const trendSearchNewConfigUpdate = obj => {
  return post(`/1688/config/update`, obj)
}

// 1688分页查询
export const crowd1688GetPage = obj => {
  return get(`crowd1688Config/getPage`, obj)
}

// 1688配置新增
export const crowd1688Save = obj => {
  return post(`crowd1688Config/save`, obj)
}

// 1688配置删除
export const crowd1688Del = id => {
  return del(`crowd1688Config/${id}`)
}

// 1688配置编辑
export const crowd1688Update = obj => {
  return post(`crowd1688Config/update`, obj)
}

// 新运营位分页查询列表
export const getBannerList = obj => {
  return get(`/banner/list/v2`, obj)
}
// 新运营位状态更新
export const bannerUpdateStatus = obj => {
  return post(`/banner/updateStatus`, obj)
}
// 获取运营位位置根据场景分组
export const getBannerLocation = obj => {
  return get(`/banner/location/groupByScene`, obj)
}
// 保存新运营位展示策略
export const strategySave = obj => {
  return post(`/banner/config/strategy/save`, obj)
}
// 运营位用户标签查询
export const labelQueryList = obj => {
  return get(`/banner/user/label/queryList`, obj)
}
// 运营位用户标签修改
export const labelQueryEdit = obj => {
  return post(`/banner/user/label/edit`, obj)
}
// 运营位展示配置策略下拉选
export const selectorList = obj => {
  return get(`/banner/config/strategy/selector`, obj)
}
// 新运营位展示策略详情
export const strategyDetail = obj => {
  return get(`/banner/config/strategy/detail`, obj)
}

export const bannerAddNew = obj => {
  return post(`/banner/add/v2`, obj)
}

export const bannerEditNew = obj => {
  return post(`/banner/edit/v2`, obj)
}
export const bannerDDetail = obj => {
  return get(`/banner/detail/v2`, obj)
}

// 新运营位统计
export const bannerStatistics = obj => {
  return get(`/count/banner/new/list`, obj)
}
// 运营位数据统计查询导出
export const banner_new_list_export = data =>
  CONSTANT.publicPath + '/count/banner/new/list/export?' + qs.stringify(data)

export const countHfivePagev347 = obj => {
  return get(`/count/hfive/page/v347`, obj)
}

export const EXPORT_Page_Graborderlistv347 = data =>
  CONSTANT.publicPath + '/count/hfive/page/export/v347?' + qs.stringify(data)

export const EXPORT_Page_Graborderlistv347Detail = data =>
  CONSTANT.publicPath + '/count/hfive/page/export/v347/detail?' + qs.stringify(data)

export const countHfivePagev347Detail = obj => {
  return get(`/count/hfive/page/v347/detail`, obj)
}

export const countLifeCycle_dateList = params => {
  return get(`/countLifeCycle/dateList`, params, null)
}

export const countLifeCycle_dateList_export = data =>
  CONSTANT.publicPath + '/countLifeCycle/dateList/export?' + qs.stringify(data)
// 搜索配置屏蔽关键词查询
export const crowd1688ShieldWord = obj => {
  return get(`/crowd1688Config/shield/word`, obj)
}

// 搜索配置屏蔽关键词新增 编辑类型 0-新增 1-删除 2-修改
export const crowd1688ShieldWordSave = obj => {
  return post(`/crowd1688Config/shield/word`, {...obj, type: 0})
}

// 搜索配置屏蔽关键词删除
export const crowd1688ShieldWordDel = obj => {
  return post(`/crowd1688Config/shield/word`, {...obj, type: 1})
}

// 广告收益统计
export const advertiseInCome = obj => {
  return get(`/count/advertise/inCome`, obj)
}
// 广告收益统计导出
export const advertiseInCome_export = data =>
  CONSTANT.publicPath + '/count/advertise/inCome/export?' + qs.stringify(data)

//  广告收益图表
export const inComeCharts = params => {
  return post(`/count/advertise/inCome/charts`, params)
}

// 搜索配置屏蔽关键词编辑
export const crowd1688ShieldWordUpdate = obj => {
  return post(`/crowd1688Config/shield/word`, {...obj, type: 2})
}

export const undertakelist = obj => {
  return get(`/channel/undertake/list`, obj)
}

export const addOrUpdate = params => {
  return post(`/channel/undertake/addOrUpdate`, params)
}

/**
 * 获取用户行为列表
 * @param {Object} obj - 请求参数 type: 0-站外落地页 1-App
 */
export const getBehaviorList = obj => {
  return get('/channel/behavior', obj)
}

/**
 * 策略管理 - 分页列表
 */
export const getStrategyPage = obj => {
  return get('/strategy/page', obj)
}

/**
 * 策略管理 - 新增策略
 */
export const addStrategy = obj => {
  return post('/strategy', obj)
}

/**
 * 策略管理 - 编辑策略
 */
export const updateStrategy = obj => {
  return put('/strategy', obj)
}

/**
 * 策略管理 - 获取全部策略列表
 */
export const getAllStrategy = () => {
  return get('/strategy')
}

/**
 * 风控管理 - 分页列表
 */
export const getChannelStrategyPage = obj => {
  return get('/channelStrategy', obj)
}

/**
 * 风控管理 - 新增风控渠道
 */
export const addChannelStrategy = obj => {
  return post('/channelStrategy', obj)
}

/**
 * 风控管理 - 删除风控渠道
 */
export const deleteChannelStrategy = id => {
  return deleteWithBody('/channelStrategy', [id])
}

// 根据类型获取承接列表
export function getUndertakeTriageSelector(params) {
  return get('/channel/undertake/triageSelector', params)
}
//查询所有的分发机构
export const triageFromList = obj => {
  return get(`/triage-record/triage/fromList`, obj)
}

// 线索分配管理列表
export const clueSendMediaConfigPage = obj => {
  return get(`/clue-send-media-config/page`, obj)
}

// 线索分配管理新增
export const clueSendMediaConfigInsert = obj => {
  return post(`/clue-send-media-config/insert`, obj)
}

// 线索分配管理详情
export const clueSendMediaConfigDetail = id => {
  return get(`/clue-send-media-config/detail/${id}`)
}

// 线索分配管理编辑
export const clueSendMediaConfigEdit = obj => {
  return post(`/clue-send-media-config/edit`, obj)
}

// 线索分配管理修改状态
export const clueSendMediaConfigStatus = ({id, status}) => {
  return post(`/clue-send-media-config/editStatus/${id}/${status}`)
}

// 获取媒体
export const getMediaList = obj => {
  return get(`/mediaDelivery/advflow/media/all/mediaType`, obj)
}

// 根据媒体类型查询投放媒体
export const getMediaListByType = obj => {
  return get(`/mediaDelivery/advflow/media/all/byMediaType`, obj)
}
// 老用户转换列表
export const getOldUserMediaConfigPage = obj => {
  return get(`/old-user-media-config/page`, obj)
}
//新增
export const addOldUserMediaConfig = obj => {
  return post(`/old-user-media-config/insert`, obj)
}
export const editOldUserMediaConfig = obj => {
  return post(`/old-user-media-config/edit`, obj)
}
export const oldUserMediaConfigById = id => {
  return post(`/old-user-media-config/detail${id}`)
}
export const editStatusOldUserMedia = obj=> {
  return post(`/old-user-media-config/editStatus/${obj.id}/${obj.status}`)
}
//撞库类型--媒体获取撞库类型和对应的APICo
export const mediaApiMatchType = obj=> {
  return get(`/old-user-media-config/api-match-type`,obj)
}