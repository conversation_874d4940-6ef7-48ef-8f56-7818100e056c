<template>
  <div>
    <page :request="request" :list="list" table-title="老用户转化规则">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small" @click="handleAdd">
          新增分配规则
        </el-button>
      </div>
    </page>
    <EditForm
      v-if="showDrawer"
      :show.sync="showDrawer"
      :modelForm="formData"
      :mediaList="mediaListOriginal"
      :mediaFromClue="mediaFromClue"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import EditForm from '@/views/operate/module/oldUserConversionManage/editForm.vue'
import page from '@/components/restructure/page'
import {tableItemType, formItemType} from '@/config/sysConfig'
import moment from 'moment'
import {
  oldUserList,
  INIT_FORM_DATA,
  judgmentMark,
  orderJudgeList,
  timeJudgeTypes,
  collisionCheck,
  crackingTypeList,
  statusList,
  handleLeafData,
  defaultRulesList
} from '@/views/operate/module/oldUserConversionManage/baseData'
import {
  editStatusOldUserMedia,
  editOldUserMediaConfig,
  addOldUserMediaConfig,
  getOldUserMediaConfigPage,
  getMediaListByType,
  mediaApiMatchType
} from '@/api/operate'
import {cloneDeep} from 'lodash'

export default {
  components: {
    page,
    EditForm
  },
  data() {
    return {
      showDrawer: false,
      formData: cloneDeep(INIT_FORM_DATA),
      mediaList: [],
      mediaListOriginal: [],
      formMediaList: [], //根据撞库类型获取的媒体列表
      listQuery: {},
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          const res = await getOldUserMediaConfigPage(this.listQuery)
          const {records, total} = res.data
          const result = {
            data: {
              total: total,
              rows: records
            }
          }
          return result
        }
      },
      statusLoading: false,
      mediaFromClue: []
    }
  },
  computed: {
    list() {
      let _this = this
      return [
        {
          title: '规则ID',
          key: 'id'
        },
        {
          title: '规则名称',
          key: 'name',
          search: true,
          titleHidden: true,
          type: formItemType.input,
          tableView: tableItemType.tableView.text,
          tableHidden: true
        },
        {
          title: '规则名称',
          key: 'name'
        },
        {
          title: '规则备注',
          key: 'remark'
        },
        {
          title: '老用户判定标识',
          key: 'judgeFiled',
          type: formItemType.select,
          list: judgmentMark,
          tableView: tableItemType.tableView.text
        },
        {
          title: '撞库媒体',
          key: 'apiCode',
          search: true,
          titleHidden: true,
          type: formItemType.input,
          tableHidden: true,
          renderCustom: (h, params, vm) => {
            return (
              <el-cascader
                value={vm.value}
                options={this.mediaList}
                clearable={true}
                filterable={true}
                placeholder="撞库媒体"
                onChange={e => {
                  vm.$emit('input', e?.[e.length - 1] || '')
                }}></el-cascader>
            )
          }
        },
        {
          title: '老用户类型',
          key: 'oldUserType',
          type: formItemType.select,
          list: oldUserList,
          search: true,
          titleHidden: true,
          tableView: tableItemType.tableView.text
        },
        {
          title: '撞库媒体',
          key: 'apiCodeList',
          render: (h, params) => {
            const apiCodeList = params.data.row.apiCodeList || []
            if (apiCodeList.length === 0) return <span>-</span>
            const mediaNames = apiCodeList
              .map(item => {
                const media = this.mediaListOriginal.find(el => el.platformCode === item.apiCode)
                return media ? media.platformName : item.apiCode
              })
              .join(', ')
            return <span>{mediaNames}</span>
          }
        },
        {
          title: '订单判定',
          key: 'orderJudge',
          type: formItemType.select,
          list: orderJudgeList,
          tableView: tableItemType.tableView.text
        },
        {
          title: '时间判定类型',
          key: 'timeJudgeType',
          type: formItemType.select,
          list: timeJudgeTypes,
          tableView: tableItemType.tableView.text
        },
        {
          title: '时间判定条件',
          key: 'timeJudgeValue',
          render: (h, params) => {
            const timeJudgeValue = params.data.row?.timeJudgeValue
            if (timeJudgeValue === null || timeJudgeValue === undefined) return <span>-</span>
            const comparison = params.data.row?.comparison
            const timeJudgeType = params.data.row?.timeJudgeType
            const typeStr = timeJudgeTypes.find(item => item.value === timeJudgeType)?.label ?? ''
            const dayStr =
              comparison === 1
                ? `超过${timeJudgeValue}天`
                : comparison === 2
                ? `${timeJudgeValue}天及以上`
                : `${timeJudgeValue}天以内`
            return (
              <span>
                {typeStr}
                {dayStr}
              </span>
            )
          }
        },
        {
          title: '是否参与撞库校验',
          key: 'isCollisionCheck',
          type: formItemType.select,
          list: collisionCheck,
          tableView: tableItemType.tableView.text
        },
        {
          title: '撞库类型',
          key: 'matchType',
          type: formItemType.select,
          list: crackingTypeList,
          tableView: tableItemType.tableView.text
        },
        {
          title: '是否默认规则',
          key: 'isDefault',
          type: formItemType.select,
          list: defaultRulesList,
          tableView: tableItemType.tableView.text
        },
        {
          title: '状态',
          key: 'status1',
          searchKey: 'status',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          list: statusList,
          tableHidden: true
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.input,
          width: 160,
          render: (h, params) => {
            const data = params.data.row
            return (
              <el-switch
                v-model={data.status}
                active-text="启用"
                inactive-text="禁用"
                active-value={1}
                inactive-value={0}
                disabled={_this.statusLoading}
                onChange={val => {
                  _this.statusLoading = true
                  editStatusOldUserMedia({id: data.id, status: val})
                    .then(res => {
                      if (res.code === 200) {
                        this.$message.success('操作成功')
                      }
                    })
                    .finally(() => {
                      this.$store.dispatch('tableRefresh', this)
                      _this.statusLoading = false
                    })
                }}></el-switch>
            )
          }
        },
        {
          title: '添加人',
          key: 'createByName'
        },
        {
          title: '添加时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return <span>-</span>
            }
            return <span>{moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '更新人',
          key: 'updateByName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return <span>-</span>
            }
            return <span>{moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                const formData = cloneDeep(params)
                const apiCodeData = formData.apiCodeList.map(item => item.apiCode)
                formData.apiCodeList = this.mediaListOriginal.reduce((pre, cur) => {
                  if (apiCodeData.includes(cur.platformCode)) {
                    pre.push([cur.mediaType, cur.platformCode])
                  }
                  return pre
                }, [])
                this.formData = formData
                this.showDrawer = true
              }
            }
          ]
        }
      ]
    }
  },
  async created() {
    let {data} = await mediaApiMatchType()
    this.mediaFromClue = data || []
    getMediaListByType().then(async res => {
      if (res.code === 200 && Array.isArray(res.data)) {
        this.mediaListOriginal = res.data
        let mediaList = this.filterMediaByMediaType(this.mediaListOriginal)
        this.mediaList = handleLeafData(mediaList)
      }
    })
  },
  methods: {
    //可配置的媒体
    filterMediaByMediaType(list = []) {
      let apiCodes = this.mediaFromClue?.map(item => item.apiCode) ?? []
      return list?.filter(item => apiCodes.includes(item.platformCode)) ?? []
    },

    handleAdd() {
      this.formData = cloneDeep(INIT_FORM_DATA)
      this.showDrawer = true
    },
    handleSubmit(formData) {
      formData.apiCodeList = formData.apiCodeList.map(item => ({apiCode: item[1], trafficType: item[0]}))
      if (formData.id) {
        editOldUserMediaConfig(formData).then(res => {
          if (res.code !== 200) {
            return
          }
          this.$message.success('编辑成功')
          this.showDrawer = false
          this.$store.dispatch('tableRefresh', this)
        })
      } else {
        addOldUserMediaConfig(formData).then(res => {
          if (res.code !== 200) {
            return
          }
          this.$message.success('新增成功')
          this.showDrawer = false
          this.$store.dispatch('tableRefresh', this)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 15px;

  .search-condition {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;

    .title {
      font-weight: bold;
      margin-bottom: 15px;
    }
  }
}

::v-deep .el-tabs__item {
  font-size: 16px;
  padding: 0 20px;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}

/* 解决 添加 filterable placeholder 重影 */
::v-deep {
  .el-cascader__tags input::-webkit-input-placeholder {
    color: white;
    opacity: 0;
  }
}
</style>
