<template>
  <div>
    <page :request="request" :list="list" table-title="线索分配管理">
      <div slot="searchContainer" style="display: inline-block; margin-bottom: 15px">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small" @click="handleAdd">
          新增分配规则
        </el-button>
      </div>
    </page>
    <el-drawer :visible.sync="showDrawer" direction="rtl" size="50%" :with-header="false" :wrapper-closable="false">
      <div class="close_button">
        <i class="el-icon-close" @click="showDrawer = false" />
      </div>
      <div class="drawer_package">
        <div class="drawer_title">
          <span>线索分配规则</span>
        </div>
        <div class="addForm_package">
          <el-form ref="form" :model="formData" :rules="formRules" label-width="120px" class="demo-ruleForm">
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line"></div>
                <span>基础信息</span>
              </div>
              <div>
                <el-form-item label="规则名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入规则名称"
                    maxlength="30"
                    show-word-limit
                    class="input-with-limit"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="规则备注" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入规则备注"
                    maxlength="100"
                    show-word-limit
                    class="input-with-limit"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line"></div>
                <span>分配规则</span>
              </div>
              <el-form-item label="分配媒体" prop="apiCodeList">
                <el-cascader
                  v-model="formData.apiCodeList"
                  :options="mediaList"
                  :props="{multiple: true, emitPath: false}"
                  clearable
                  filterable
                ></el-cascader>
              </el-form-item>
              <el-form-item label="分配规则" prop="mediaConfigDetailList">
                <el-table :data="formData.mediaConfigDetailList" border style="width: 100%">
                  <el-table-column props="checked" width="55">
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.checked" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="sendTo" label="分配方">
                    <template slot-scope="scope">
                      {{ getSendToLabel(scope.row.sendTo) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="toDayMax" label="当日分配上线">
                    <template slot="header">
                      <span>当日分配上限</span>
                      <el-tooltip :style="{color: '#409eff'}">
                        <div slot="content">
                          （注意：该分配数量为自然日维度计数，若当日线索数量不足，可在此基础上累加）
                        </div>
                        <i class="el-icon-question" style="font-size: 14px" />
                      </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.toDayMax" placeholder="请输入当日分配上线" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="distributionRatio" label="分配比例">
                    <template slot="header">
                      <span>分配比例</span>
                      <el-tooltip :style="{color: '#409eff'}">
                        <div slot="content">
                          （注意：设置分配后，参与分配的媒体将按照比例分配至分配方，分配比例未按照最小质数原则分配）
                        </div>
                        <i class="el-icon-question" style="font-size: 14px" />
                      </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.distributionRatio" placeholder="请输入分配比例" size="small" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
            <div class="form_view">
              <div class="form_view_title">
                <div class="title_line"></div>
                <span>状态</span>
              </div>
              <el-form-item label="状态" prop="status">
                <el-radio v-model="formData.status" :label="1">启用</el-radio>
                <el-radio v-model="formData.status" :label="0">禁用</el-radio>
              </el-form-item>
            </div>
            <div :style="{'text-align': 'right', width: '100%'}">
              <el-button @click="showDrawer = false">取消</el-button>
              <el-button type="primary" @click="handleSubmit()">确认</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import {tableItemType, formItemType} from '@/config/sysConfig'
import moment from 'moment'
import {
  clueSendMediaConfigPage,
  clueSendMediaConfigInsert,
  clueSendMediaConfigEdit,
  clueSendMediaConfigStatus,
  getMediaListByType
} from '@/api/operate'
import {cloneDeep} from 'lodash'

// 分配方枚举
const SEND_TO_ENUM = {
  OWN_CRM: 0, // 自有CRM
  YI_SHUN: 1 // 易顺
}

// 分配方标签映射
const SEND_TO_LABELS = {
  [SEND_TO_ENUM.OWN_CRM]: '自有CRM',
  [SEND_TO_ENUM.YI_SHUN]: '易顺'
}

const INIT_FORM_DATA = {
  name: '',
  remark: '',
  apiCodeList: [],
  mediaConfigDetailList: [
    {
      sendTo: 1,
      toDayMax: '',
      distributionRatio: '',
      checked: false
    },
    {
      sendTo: 0,
      toDayMax: '',
      distributionRatio: '',
      checked: false
    }
  ],
  status: 1
}

export default {
  components: {
    page
  },
  data() {
    return {
      showDrawer: false,
      formData: cloneDeep(INIT_FORM_DATA),
      formRules: {
        name: [{required: true, message: '请输入策略名称', trigger: 'blur'}],
        apiCodeList: [{required: true, message: '请选择分配媒体', trigger: 'change'}],
        mediaConfigDetailList: [
          {required: true, message: '请输入分配规则', trigger: 'change'},
          {
            validator: (rule, value, callback) => {
              for (let i = 0; i < value.length; i++) {
                if (value[i].checked) {
                  if (!value[i].toDayMax) {
                    callback(new Error('请输入当日分配上线'))
                    return
                  }
                  if (!value[i].distributionRatio) {
                    callback(new Error('请输入分配比例'))
                    return
                  }
                  // 验证当日分配上线是否为正整数
                  if (!/^[1-9]\d*$/.test(value[i].toDayMax)) {
                    callback(new Error('当日分配上线必须为正整数'))
                    return
                  }
                  // 验证分配比例是否为正整数
                  if (!/^[1-9]\d*$/.test(value[i].distributionRatio)) {
                    callback(new Error('分配比例必须为正整数'))
                    return
                  }
                }
              }
              callback()
            }
          }
        ],
        status: [{required: true, message: '请选择状态', trigger: 'change'}]
      },

      tableData: [],

      mediaList: [],
      mediaListOriginal: [],

      listQuery: {
        stratCreateTime: moment().subtract(365, 'days').format('YYYY-MM-DD'),
        endCreateTime: moment().format('YYYY-MM-DD')
      },

      // API请求
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          const res = await clueSendMediaConfigPage(this.listQuery)
          const {records, total} = res.data
          const result = {
            data: {
              total: total,
              rows: records
            }
          }
          return result
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return <span>{params.data.$index + 1}</span>
          }
        },
        {
          title: '添加时间',
          key: 'createTime',
          type: formItemType.rangeDatePicker,
          childKey: ['stratCreateTime', 'endCreateTime'],
          options: {
            format: 'YYYY-MM-DD',
            valueFormat: 'yyyy-MM-dd'
          },
          val: [this.listQuery.stratCreateTime, this.listQuery.endCreateTime],
          search: true,
          tableHidden: true,
          pickerDay: 30
        },
        {
          title: '规则名称',
          key: 'name',
          search: true,
          type: formItemType.input,
          tableView: tableItemType.tableView.text,
          tableHidden: true
        },
        {
          title: '规则名称',
          key: 'name'
        },
        {
          title: '规则备注',
          key: 'remark'
        },
        {
          title: '分配媒体',
          key: 'apiCode',
          search: true,
          type: formItemType.input,
          tableHidden: true,
          renderCustom: (h, params, vm) => {
            return (
              <el-cascader
                value={vm.value}
                options={this.mediaList}
                clearable={true}
                filterable={true}
                onChange={e => {
                  vm.$emit('input', e[e.length - 1])
                }}></el-cascader>
            )
          }
        },
        {
          title: '分配方',
          key: 'mediaConfigDetailList',
          render: (h, params) => {
            const mediaConfigDetailList = params.data.row.mediaConfigDetailList || []
            if (mediaConfigDetailList.length === 0) return <span>-</span>

            const sendToInfo = mediaConfigDetailList.map(config => this.getSendToLabel(config.sendTo)).join(', ')

            return <span>{sendToInfo}</span>
          }
        },
        {
          title: '分配媒体',
          key: 'apiCodeList',
          render: (h, params) => {
            const apiCodeList = params.data.row.apiCodeList || []
            if (apiCodeList.length === 0) return <span>-</span>

            const mediaNames = apiCodeList
              .map(apiCode => {
                const media = this.mediaListOriginal.find(item => item.platformCode === apiCode)
                return media ? media.platformName : apiCode
              })
              .join(', ')

            return <span>{mediaNames}</span>
          }
        },
        {
          title: '添加人',
          key: 'createByName'
        },
        {
          title: '添加时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return <span>-</span>
            }
            return <span>{moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '更新人',
          key: 'updateByName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return <span>-</span>
            }
            return <span>{moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss')}</span>
          }
        },
        {
          title: '状态',
          key: 'status',
          search: true,
          type: formItemType.select,
          list: [
            {
              label: '启用',
              value: 1
            },
            {
              label: '禁用',
              value: 0
            }
          ],
          tableHidden: true
        },
        {
          title: '状态',
          key: 'status',
          type: formItemType.input,
          render: (h, params) => {
            const data = params.data.row
            return (
              <el-switch
                v-model={data.status}
                active-text="启用"
                inactive-text="禁用"
                active-value={1}
                inactive-value={0}
                onChange={val => {
                  clueSendMediaConfigStatus({id: data.id, status: val})
                    .then(res => {
                      if (res.code === 200) {
                        this.$message.success('操作成功')
                      }
                    })
                    .finally(() => {
                      this.$store.dispatch('tableRefresh', this)
                    })
                }}></el-switch>
            )
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: ($index, item, params) => {
                const formData = cloneDeep(params)

                this.sortApiCodeList(formData.apiCodeList, this.mediaListOriginal)

                formData.mediaConfigDetailList = INIT_FORM_DATA.mediaConfigDetailList.map((item, index) => {
                  const rowItem = formData.mediaConfigDetailList.find(media => media.sendTo === item.sendTo)
                  const curItem = {
                    ...item
                  }

                  if (rowItem) {
                    curItem.distributionRatio = rowItem.distributionRatio
                    curItem.toDayMax = rowItem.toDayMax
                  }

                  return {
                    ...curItem,
                    checked: !!rowItem
                  }
                })

                this.formData = formData
                this.showDrawer = true
              }
            }
          ]
        }
      ]
    }
  },
  watch: {
    showDrawer(val) {
      if (!val) {
        this.formData = cloneDeep(INIT_FORM_DATA)
        this.$refs.form.resetFields()
      }
    }
  },
  created() {
    getMediaListByType().then(res => {
      if (res.code === 200 && Array.isArray(res.data)) {
        this.mediaListOriginal = res.data
        this.mediaList = res.data.reduce((acc, cur) => {
          const {mediaType} = cur

          const i = acc.findIndex(item => item.value === mediaType)
          if (i > -1) {
            acc[i].children.push({label: cur.platformName, value: cur.platformCode})
          } else {
            acc.push({
              label: mediaType,
              value: mediaType,
              children: [{label: cur.platformName, value: cur.platformCode}]
            })
          }

          return acc
        }, [])
      }
    })
  },
  methods: {
    // 获取分配方标签
    getSendToLabel(sendTo) {
      return SEND_TO_LABELS[sendTo] || '-'
    },

    handleAdd() {
      this.formData = cloneDeep(INIT_FORM_DATA)
      this.showDrawer = true
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = cloneDeep(this.formData)

          formData.mediaConfigDetailList = formData.mediaConfigDetailList
            .filter(item => item.checked)
            .map(item => {
              const {checked, ...rest} = item
              return rest
            })

          if (formData.id) {
            clueSendMediaConfigEdit(formData).then(res => {
              if (res.code !== 200) {
                return
              }
              this.$message.success('编辑成功')
              this.showDrawer = false
              this.$refs.form.resetFields()
              this.$store.dispatch('tableRefresh', this)
            })
          } else {
            clueSendMediaConfigInsert(formData).then(res => {
              if (res.code !== 200) {
                return
              }
              this.$message.success('新增成功')
              this.showDrawer = false
              this.$refs.form.resetFields()
              this.$store.dispatch('tableRefresh', this)
            })
          }
        }
      })
    },
    /**
     * 对apiCodeList排序
     */
    sortApiCodeList(val1, val2) {
      // 修改val1排序，按照val2的顺序进行排序，val2中的key值是platformCode
      val1.sort((a, b) => {
        const index1 = val2.findIndex(item => item.platformCode === a)
        const index2 = val2.findIndex(item => item.platformCode === b)
        return index1 - index2
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  overflow: scroll;
  padding: 0 30px 20px;
  position: relative;
}

.close_button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: rgb(0, 0, 0, 1);
  text-align: center;
  cursor: pointer;

  i {
    color: white;
    line-height: 40px;
  }
}

.drawer_package {
  height: 100%;
  position: relative;

  .drawer_title {
    padding: 10px 20px 5px;
    vertical-align: middle;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.addForm_package {
  background-color: rgb(189, 184, 184, 0.1);
  padding: 15px;
  height: 100%;

  .demo-ruleForm {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    position: relative;
  }
}

.form_view {
  background-color: #ffffff;
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;

  .form_view_title {
    margin-bottom: 20px;

    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}

.search-container {
  margin-bottom: 15px;

  .search-condition {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;

    .title {
      font-weight: bold;
      margin-bottom: 15px;
    }
  }
}

::v-deep .el-tabs__item {
  font-size: 16px;
  padding: 0 20px;
}

::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}

.channel-input-group {
  display: flex;
  align-items: center;

  .el-button {
    margin-left: 10px;
  }
}

.added-channels-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;

  .added-channels-header {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;

    .link-text {
      color: #409eff;
      margin-left: 15px;
      cursor: pointer;
    }
  }
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.required-asterisk {
  color: #f56c6c;
  margin-right: 4px;
  content: '*';
}

.input-with-limit {
  ::v-deep .el-input__inner {
    padding-right: 50px;
  }
}
/* 解决 添加 filterable placeholder 重影 */
::v-deep {
  .el-cascader__tags input::-webkit-input-placeholder {
    color: white;
    opacity: 0;
  }
}
</style>
