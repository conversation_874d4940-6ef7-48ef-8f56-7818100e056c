<template>
  <div class="base-input">
    <!-- 
      使用 v-bind="$attrs" 进行属性透传
      使用 v-on="$listeners" 进行事件透传
      ref="elInput" 用于获取原始组件实例
    -->
    <el-input
      ref="elInput"
      v-bind="$attrs"
      v-on="$listeners"
      :value="value"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @clear="handleClear"
    >
      <!-- 插槽透传：使用动态插槽名称透传所有插槽 -->
      <template v-for="(_, slot) of $scopedSlots" v-slot:[slot]="scope">
        <slot :name="slot" v-bind="scope" />
      </template>
      
      <!-- 具名插槽透传（兼容旧版本Vue） -->
      <template slot="prepend" v-if="$slots.prepend">
        <slot name="prepend" />
      </template>
      
      <template slot="append" v-if="$slots.append">
        <slot name="append" />
      </template>
      
      <template slot="prefix" v-if="$slots.prefix">
        <slot name="prefix" />
      </template>
      
      <template slot="suffix" v-if="$slots.suffix">
        <slot name="suffix" />
      </template>
    </el-input>
    
    <!-- 自定义扩展功能 -->
    <div v-if="showWordCount && maxlength" class="word-count">
      {{ currentLength }}/{{ maxlength }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseInput',
  
  // 禁用属性继承，手动控制属性透传
  inheritAttrs: false,
  
  props: {
    // 自定义props
    value: {
      type: [String, Number],
      default: ''
    },
    showWordCount: {
      type: Boolean,
      default: false
    },
    // 可以添加其他自定义属性
    validateOnChange: {
      type: Boolean,
      default: true
    }
  },
  
  computed: {
    // 获取maxlength属性
    maxlength() {
      return this.$attrs.maxlength
    },
    
    // 计算当前字符长度
    currentLength() {
      return String(this.value || '').length
    }
  },
  
  methods: {
    // 事件处理方法
    handleInput(value) {
      this.$emit('input', value)
      // 可以添加自定义逻辑
      this.customValidate(value)
    },
    
    handleChange(value) {
      this.$emit('change', value)
      if (this.validateOnChange) {
        this.customValidate(value)
      }
    },
    
    handleBlur(event) {
      this.$emit('blur', event)
    },
    
    handleFocus(event) {
      this.$emit('focus', event)
    },
    
    handleClear() {
      this.$emit('clear')
      this.$emit('input', '')
    },
    
    // 自定义验证方法
    customValidate(value) {
      // 自定义验证逻辑
      if (this.maxlength && value.length > this.maxlength) {
        this.$emit('exceed-limit', value)
      }
    },
    
    // 暴露原始组件的所有方法
    focus() {
      return this.$refs.elInput.focus()
    },
    
    blur() {
      return this.$refs.elInput.blur()
    },
    
    select() {
      return this.$refs.elInput.select()
    },
    
    clear() {
      return this.$refs.elInput.clear()
    },
    
    // 获取原始组件实例
    getElInputInstance() {
      return this.$refs.elInput
    },
    
    // 代理原始组件的所有方法（动态代理）
    ...(() => {
      const methods = {}
      // 这里可以根据需要添加更多Element UI Input的方法
      const elInputMethods = ['focus', 'blur', 'select', 'clear']
      
      elInputMethods.forEach(method => {
        methods[method] = function(...args) {
          if (this.$refs.elInput && typeof this.$refs.elInput[method] === 'function') {
            return this.$refs.elInput[method](...args)
          }
        }
      })
      
      return methods
    })()
  }
}
</script>

<style lang="scss" scoped>
.base-input {
  position: relative;
  
  .word-count {
    position: absolute;
    right: 10px;
    bottom: -20px;
    font-size: 12px;
    color: #909399;
  }
}
</style>
