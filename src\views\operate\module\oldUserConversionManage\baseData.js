export const oldUserList = [
  // {
  //   label: '老用户-登录',
  //   value: 1
  // },
  {
    label: '老用户-采量',
    value: 2
  }
]
// 线索管理
export const SEND_TO_ENUM = {
  OWN_CRM: 1, // 自有CRM
  YI_SHUN: 3, // 易顺CRM
  OWN_DISTRIBUTE: 2 //自有分发库
}
//撞库类型
export const CRACKING_TYPE = {
  CLUE: 1, // 线索撞库
  DISTRIBUTE: 2, // 分发撞库
  ALL: 3 // 线索/分发撞库
}
// 线索管理签映射
export const SEND_TO_LABELS = {
  [SEND_TO_ENUM.OWN_CRM]: '自有crm库',
  [SEND_TO_ENUM.YI_SHUN]: '易顺crm库',
  [SEND_TO_ENUM.OWN_DISTRIBUTE]: '自有分发库'
}
export const judgmentMark = [
  {
    label: '手机号',
    value: 'mobile_no'
  }
]
export const judgmentCondition = [
  {
    label: '用户已创建',
    value: 1
  }
]
export const orderJudgeList = [{label: '用户无订单/订单已完结', value: 1}]
export const timeJudgeTypes = [
  {
    label: '注册时间',
    value: 1
  },
  {
    label: '订单创建时间',
    value: 2
  }
]
export const timeJudgeCondition = [
  {
    label: '大于',
    value: 1
  },
  {
    label: '大于等于',
    value: 2
  },
  {
    label: '小于',
    value: 3
  }
]
export const crackingTypeList = [
  {
    label: '线索撞库',
    value: CRACKING_TYPE.CLUE
  },
  {
    label: '分发撞库',
    value: CRACKING_TYPE.DISTRIBUTE
  },
  {
    label: '线索撞库/分发撞库',
    value: CRACKING_TYPE.ALL
  }
]
export const CLUEDATA_TYPE ={
  ANY_ONE:1,
  ALL:2,
  CUSTOM:3
}

export const clueDatabaseTypeList = [
  {label: 'N选一', value:CLUEDATA_TYPE.ANY_ONE, tips: '线索库中任意一个库撞库成功即认为撞库成功'},
  {label: '全部', value: CLUEDATA_TYPE.ALL, tips: '线索库所有库撞库成功即认为撞库成功'},
  {label: '自定义', value: CLUEDATA_TYPE.CUSTOM, tips: '选中的线索库将参与撞库，已选库撞库成功即认为撞库成功'}
]
export const clueDataList = [
  {
    clueType: CRACKING_TYPE.CLUE,
    clueTypeName: '',
    clueValue: SEND_TO_ENUM.OWN_CRM,
    clueName: SEND_TO_LABELS[SEND_TO_ENUM.OWN_CRM]
  },
  {
    clueType: CRACKING_TYPE.CLUE,
    clueTypeName: '',
    clueValue: SEND_TO_ENUM.YI_SHUN,
    clueName: SEND_TO_LABELS[SEND_TO_ENUM.YI_SHUN]
  },
  {
    clueType: CRACKING_TYPE.DISTRIBUTE,
    clueTypeName: '',
    clueValue: SEND_TO_ENUM.OWN_DISTRIBUTE,
    clueName: SEND_TO_LABELS[SEND_TO_ENUM.OWN_DISTRIBUTE]
  }
]
export const DEFAULT_RULES_MAP={
  YES:1,
  NO:0
}
export const defaultRulesList = [
  {
    label: '是',
    value: DEFAULT_RULES_MAP.YES
  },
  {
    label: '否',
    value: DEFAULT_RULES_MAP.NO
  }
]
export const INIT_FORM_DATA = {
  name: '',
  remark: '',
  judgeFiled: '',
  oldUserType: '',
  judgeType: '',
  apiCodeList: [],
  orderJudge: 1,
  timeJudgeType: '',
  comparison: '',
  timeJudgeValue: '',
  isCollisionCheck: null,
  matchType: null,
  apiMatchType: null, //撞库类型
  clueLogic: null,
  thirdMatchS: [],
  isDefault: null,
  status: 1
}
export const collisionCheck = [
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 0
  }
]
export const statusList = [
  {
    label: '启用',
    value: 1
  },
  {
    label: '禁用',
    value: 0
  }
]
export const clueMatchTypeList = [
  {label: 'MD5撞库', value: 0},
  {label: '掩码撞库', value: 1}
]
export function handleLeafData(list) {
  if (Array.isArray(list) && list.length > 0) {
    let pre = list.reduce((acc, cur) => {
      const {mediaType} = cur
      const i = acc.findIndex(item => item.value === mediaType)
      if (i > -1) {
        acc[i].children.push({label: cur.platformName, value: cur.platformCode})
      } else {
        acc.push({
          label: mediaType,
          value: mediaType,
          children: [{label: cur.platformName, value: cur.platformCode}]
        })
      }
      return acc
    }, [])
    return pre
  } else {
    return []
  }
}
